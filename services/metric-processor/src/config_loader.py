"""
This module handles loading and managing metric configurations from Postgres and memory.
It provides functionality to load and manage configs per tenant/facility/fact_type.
"""

from typing import Dict

import psycopg2
import structlog
from pydantic import ValidationError

from src.schemas import config_schema
from src.services.postgres_factory import PostgresFactory

logger = structlog.get_logger(__name__)


class ConfigLoader:
    """
    Handles loading and managing metric configurations.

    This class is responsible for:
    1. Loading configs from Postgres for each tenant/facility/fact_type
    2. Managing configs in memory
    3. Handling config validation

    This is implemented as a singleton to ensure config caching is shared across all requests
    within a CloudRun instance.
    """

    # =============== Singleton Implementation ===============
    _instance = None
    _initialized = False

    def __new__(cls):
        """
        Returns the singleton instance of the ConfigLoader class,
        creating it if it doesn't exist.
        """
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """
        Initialize the ConfigLoader class.
        Sets up the configs dictionary on first initialization only.
        """
        if not self._initialized:
            # Structure: configs[tenant_id][facility_id][fact_type][metric_config_name] = config
            self.configs = {}
            ConfigLoader._initialized = True

    @classmethod
    def reset(cls) -> None:
        """
        Reset the singleton instance.
        This should only be used in testing or when absolutely necessary.
        """
        cls._instance = None
        cls._initialized = False

    def _validate_parameters(self, tenant: str, facility: str, fact_type: str) -> bool:
        """Validate input parameters and log errors if invalid."""
        validation_errors = []

        if not tenant or not isinstance(tenant, str):
            validation_errors.append(("tenant", tenant, type(tenant)))
        if not facility or not isinstance(facility, str):
            validation_errors.append(("facility", facility, type(facility)))
        if not fact_type or not isinstance(fact_type, str):
            validation_errors.append(("fact_type", fact_type, type(fact_type)))

        for param_name, param_value, param_type in validation_errors:
            logger.error(
                f"Invalid {param_name} parameter",
                **{param_name: param_value, f"{param_name}_type": param_type},
            )

        return len(validation_errors) == 0

    def _get_cached_config(
        self, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Get configuration from cache if available."""
        if (
            tenant in self.configs
            and facility in self.configs[tenant]
            and fact_type in self.configs[tenant][facility]
        ):
            return self.configs[tenant][facility][fact_type]
        return None

    def _process_metric_config_rows(
        self, metric_config_rows: list, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Process metric config rows and build the fact_type metrics dict."""
        fact_type_metrics_dict = {}

        for row in metric_config_rows:
            try:
                config_data = row.get("config_data", row)
                metric_config = config_schema.MetricConfig.model_validate(config_data)
                fact_type_metrics_dict[metric_config.metric_config_name] = metric_config
            except (ValueError, KeyError) as e:
                logger.error(
                    "Configuration validation error processing metric config row",
                    exc_info=True,
                    row=row,
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                    error_type=type(e).__name__,
                )
                continue
            except (ValidationError, TypeError, AttributeError) as e:
                logger.error(
                    "Unexpected error processing metric config row",
                    exc_info=True,
                    row=row,
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                    error_type=type(e).__name__,
                )
                continue

        return fact_type_metrics_dict

    # =============== Public Interface ===============

    async def get_config_by_fact_type(
        self,
        tenant: str,
        facility: str,
        fact_type: str,
        clear_fact_type_cache: bool = False,
    ) -> Dict[str, config_schema.MetricConfig]:
        """
        Get metric configurations for a specific fact type, using in-memory cache if available.
        We can optionally clear the fact type cache in runtime to force a reload of the
        fact type config from Postgres.
        """
        # Validate input parameters
        if not self._validate_parameters(tenant, facility, fact_type):
            return {}

        if clear_fact_type_cache:
            self.clear_cache_by_fact_type(tenant, facility, fact_type)

        # Check in-memory cache first
        cached_config = self._get_cached_config(tenant, facility, fact_type)
        if cached_config is not None:
            return cached_config

        # Load from Postgres
        return await self._load_config_from_postgres(tenant, facility, fact_type)

    def clear_cache(self) -> None:
        """
        Clear the entire config cache in runtime.
        This should be called when configs need to be reloaded.
        """
        self.configs = {}
        logger.info("Config cache cleared")

    def clear_cache_by_fact_type(
        self, tenant: str, facility: str, fact_type: str
    ) -> None:
        """
        Clear the config cache for a specific fact type.
        """
        if (
            tenant in self.configs
            and facility in self.configs[tenant]
            and fact_type in self.configs[tenant][facility]
        ):
            del self.configs[tenant][facility][fact_type]
            logger.info(
                "Config cache cleared for fact type",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
        else:
            logger.warning(
                "Config cache not found for fact type",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )

    # =============== Private Implementation ===============

    async def _load_config_from_postgres(
        self, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """Load configuration from Postgres and cache it."""
        logger.debug(
            f"Loading metric configs for fact type {fact_type} "
            f"from Postgres for tenant {tenant} and facility {facility}",
            tenant=tenant,
            facility=facility,
            fact_type=fact_type,
        )

        try:
            postgres_service = PostgresFactory.get_instance(tenant)
            if not postgres_service:
                logger.error(
                    f"Failed to get Postgres service for tenant {tenant} and facility {facility}",
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                return {}
        except ConnectionError as e:
            logger.error(
                f"Error creating Postgres service for tenant {tenant} and facility {facility}",
                exc_info=True,
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
                error_type=type(e).__name__,
            )
            return {}

        try:
            metric_config_rows = await postgres_service.get_metric_configs(
                tenant=tenant,
                facility_id=facility,
                fact_type=fact_type,
            )

            if not metric_config_rows:
                logger.warning(
                    f"No metric configs found for fact type {fact_type}",
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                return {}

            fact_type_metrics_dict = self._process_metric_config_rows(
                metric_config_rows, tenant, facility, fact_type
            )

            # Store in cache
            self.configs.setdefault(tenant, {}).setdefault(facility, {})[
                fact_type
            ] = fact_type_metrics_dict

            return fact_type_metrics_dict

        except (psycopg2.Error, psycopg2.OperationalError) as e:
            logger.error(
                f"Database error getting metric configs for fact type {fact_type} "
                f"from Postgres for tenant {tenant} and facility {facility}",
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise
        except (ValueError, KeyError) as e:
            logger.error(
                f"Configuration error getting metric configs for fact type {fact_type} "
                f"from Postgres for tenant {tenant} and facility {facility}",
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise
        except Exception as e:
            logger.error(
                f"Unexpected error getting metric configs for fact type {fact_type} "
                f"from Postgres for tenant {tenant} and facility {facility}",
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
                error_type=type(e).__name__,
            )
            raise

    def clear_cache(self) -> None:
        """
        Clear the entire config cache in runtime.
        This should be called when configs need to be reloaded.
        """
        self.configs = {}
        logger.info("Config cache cleared")

    def clear_cache_by_fact_type(
        self, tenant: str, facility: str, fact_type: str
    ) -> None:
        """
        Clear the config cache for a specific fact type.
        """
        if (
            tenant in self.configs
            and facility in self.configs[tenant]
            and fact_type in self.configs[tenant][facility]
        ):
            del self.configs[tenant][facility][fact_type]
            logger.info(
                "Config cache cleared for fact type",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
        else:
            logger.warning(
                "Config cache not found for fact type",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )
